# AWS Deployment Guide for CTR Road Name Translation

This guide provides comprehensive instructions for deploying the CTR Road Name Translation application to AWS using Terraform and automated deployment scripts.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Infrastructure Setup](#infrastructure-setup)
3. [Application Deployment](#application-deployment)
4. [Configuration](#configuration)
5. [Monitoring and Maintenance](#monitoring-and-maintenance)
6. [Troubleshooting](#troubleshooting)
7. [Security Considerations](#security-considerations)

## Prerequisites

### Required Software

- **Terraform** (>= 1.0): [Installation Guide](https://learn.hashicorp.com/tutorials/terraform/install-cli)
- **AWS CLI** (>= 2.0): [Installation Guide](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)
- **SSH Client**: For connecting to EC2 instances
- **Node.js** (>= 18): For local development and building
- **Yarn**: Package manager

### AWS Account Setup

1. **AWS Account**: Ensure you have an active AWS account
2. **IAM User**: Create an IAM user with the following permissions:
   - EC2FullAccess
   - VPCFullAccess
   - IAMReadOnlyAccess (for key pair management)
3. **AWS CLI Configuration**:
   ```bash
   aws configure
   # Enter your Access Key ID, Secret Access Key, and preferred region
   ```

### SSH Key Pair

Generate an SSH key pair for EC2 access:
```bash
ssh-keygen -t rsa -b 4096 -f ~/.ssh/ctr-road-name-key
chmod 600 ~/.ssh/ctr-road-name-key
```

## Infrastructure Setup

### 1. Configure Terraform Variables

Copy the example variables file and customize it:
```bash
cd terraform
cp terraform.tfvars.example terraform.tfvars
```

Edit `terraform.tfvars` with your specific values:
```hcl
# AWS Configuration
aws_region = "ap-southeast-1"  # Your preferred region

# Project Configuration
project_name = "ctr-road-name"
environment  = "production"

# Network Configuration
vpc_cidr           = "10.0.0.0/16"
public_subnet_cidr = "********/24"

# EC2 Configuration
instance_type      = "t3a.small"
root_volume_size   = 20
node_version       = "18"
app_port           = 3031

# Security Configuration (IMPORTANT: Restrict these!)
allowed_ssh_cidr_blocks = ["YOUR_IP/32"]  # Replace with your IP
allowed_app_cidr_blocks = ["0.0.0.0/0"]   # Or restrict as needed

# SSH Key Configuration
create_key_pair    = true
public_key_content = "ssh-rsa AAAAB3NzaC1yc2E... [content of ~/.ssh/ctr-road-name-key.pub]"
```

### 2. Initialize and Deploy Infrastructure

```bash
cd terraform

# Initialize Terraform
terraform init

# Review the planned changes
terraform plan

# Apply the infrastructure
terraform apply
```

### 3. Note the Outputs

After successful deployment, Terraform will output important information:
```
instance_public_ip = "*******"
ssh_command = "ssh -i ~/.ssh/ctr-road-name-key ubuntu@*******"
application_url = "http://*******:3031"
```

## Application Deployment

### 1. Wait for Instance Initialization

The EC2 instance needs time to complete the user data script. Check the status:
```bash
# SSH to the instance
ssh -i ~/.ssh/ctr-road-name-key ubuntu@YOUR_INSTANCE_IP

# Check user data script completion
sudo tail -f /var/log/user-data.log

# Verify services are running
sudo systemctl status redis-server
pm2 status
```

### 2. Deploy the Application

Use the automated deployment script:
```bash
# From the project root directory
./deploy_aws.sh --host YOUR_INSTANCE_IP --key ~/.ssh/ctr-road-name-key --env aws-production
```

### 3. Verify Deployment

Check that the application is running:
```bash
# Check PM2 status
ssh -i ~/.ssh/ctr-road-name-key ctrapp@YOUR_INSTANCE_IP "pm2 status"

# Run health check
ssh -i ~/.ssh/ctr-road-name-key ctrapp@YOUR_INSTANCE_IP "/opt/ctr-road-name/scripts/health-check.sh"

# Test the application
curl http://YOUR_INSTANCE_IP:3031
```