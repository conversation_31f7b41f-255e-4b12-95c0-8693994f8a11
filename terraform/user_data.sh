#!/bin/bash

# User data script for CTR Road Name Translation EC2 instance
# This script sets up Redis, Node.js, PM2, and other required software

set -e

# Log all output
exec > >(tee /var/log/user-data.log)
exec 2>&1

echo "Starting user data script execution at $(date)"

# Update system packages
echo "Updating system packages..."
apt-get update -y
apt-get upgrade -y

# Install essential packages
echo "Installing essential packages..."
apt-get install -y \
    curl \
    wget \
    git \
    unzip \
    build-essential \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    htop \
    nano \
    vim \
    ufw \
    fail2ban

# Configure firewall
echo "Configuring UFW firewall..."
ufw --force enable
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow ${app_port}/tcp
ufw allow 80/tcp
ufw allow 443/tcp

# Install Redis
echo "Installing Redis server..."
apt-get install -y redis-server

# Configure Redis
echo "Configuring Redis..."
sed -i 's/^bind 127.0.0.1 ::1/bind 127.0.0.1/' /etc/redis/redis.conf
sed -i 's/^# maxmemory <bytes>/maxmemory 256mb/' /etc/redis/redis.conf
sed -i 's/^# maxmemory-policy noeviction/maxmemory-policy allkeys-lru/' /etc/redis/redis.conf

# Enable and start Redis
systemctl enable redis-server
systemctl start redis-server

# Install Node.js
echo "Installing Node.js ${node_version}..."
curl -fsSL https://deb.nodesource.com/setup_${node_version}.x | bash -
apt-get install -y nodejs

# Verify Node.js installation
echo "Node.js version: $(node --version)"
echo "NPM version: $(npm --version)"

# Install PM2 globally
echo "Installing PM2 process manager..."
npm install -g pm2

# Install Yarn globally
echo "Installing Yarn package manager..."
npm install -g yarn

# Create application user
echo "Creating application user..."
useradd -m -s /bin/bash ctrapp
usermod -aG sudo ctrapp

# Create application directory
echo "Creating application directory..."
mkdir -p /opt/ctr-road-name
chown ctrapp:ctrapp /opt/ctr-road-name

# Create logs directory
mkdir -p /var/log/ctr-road-name
chown ctrapp:ctrapp /var/log/ctr-road-name

# Configure PM2 startup
echo "Configuring PM2 startup..."
sudo -u ctrapp pm2 startup systemd -u ctrapp --hp /home/<USER>
systemctl enable pm2-ctrapp

# Install AWS CLI (for potential S3 operations)
echo "Installing AWS CLI..."
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install
rm -rf aws awscliv2.zip

# Configure fail2ban for SSH protection
echo "Configuring fail2ban..."
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 3
EOF

systemctl enable fail2ban
systemctl start fail2ban

echo "User data script completed successfully at $(date)"
echo "System is ready for application deployment"