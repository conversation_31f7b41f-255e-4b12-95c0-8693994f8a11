#!/usr/bin/env bash

# AWS Deployment Script for CTR Road Name Translation
# This script deploys the application to the AWS EC2 instance created by Terraform

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_PATH="build"
REMOTE_PATH="/var/www/ctr-road-name"
REMOTE_USER="ubuntu"
APP_NAME="roadname"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --host HOST        EC2 instance public IP or hostname (required)"
    echo "  -k, --key KEY_FILE     Path to SSH private key file (required)"
    echo "  -e, --env ENV          Environment (dev, uat, production) [default: production]"
    echo "  -p, --port PORT        SSH port [default: 22]"
    echo "  --skip-build          Skip the build process"
    echo "  --skip-deps           Skip dependency installation"
    echo "  --help                Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --host ******* --key ~/.ssh/ctr-road-name-key"
    echo "  $0 -h ec2-instance.amazonaws.com -k ~/.ssh/my-key.pem -e production"
}

# Parse command line arguments
REMOTE_HOST=""
SSH_KEY=""
ENVIRONMENT="production"
SSH_PORT="22"
SKIP_BUILD=false
SKIP_DEPS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            REMOTE_HOST="$2"
            shift 2
            ;;
        -k|--key)
            SSH_KEY="$2"
            shift 2
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -p|--port)
            SSH_PORT="$2"
            shift 2
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-deps)
            SKIP_DEPS=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$REMOTE_HOST" ]]; then
    log_error "Remote host is required. Use --host option."
    show_usage
    exit 1
fi

if [[ -z "$SSH_KEY" ]]; then
    log_error "SSH key file is required. Use --key option."
    show_usage
    exit 1
fi

if [[ ! -f "$SSH_KEY" ]]; then
    log_error "SSH key file not found: $SSH_KEY"
    exit 1
fi

# SSH command template
SSH_CMD="ssh -i $SSH_KEY -p $SSH_PORT -o StrictHostKeyChecking=no $REMOTE_USER@$REMOTE_HOST"
SCP_CMD="scp -i $SSH_KEY -P $SSH_PORT -o StrictHostKeyChecking=no"

log_info "Starting deployment to $REMOTE_HOST"
log_info "Environment: $ENVIRONMENT"
log_info "SSH Key: $SSH_KEY"

# Function to test SSH connection
test_ssh_connection() {
    log_info "Testing SSH connection..."
    if $SSH_CMD "echo 'SSH connection successful'" > /dev/null 2>&1; then
        log_success "SSH connection established"
    else
        log_error "Failed to establish SSH connection"
        exit 1
    fi
}

# Function to build the application
build_application() {
    if [[ "$SKIP_BUILD" == true ]]; then
        log_warning "Skipping build process"
        return
    fi

    log_info "Building application..."

    # Install dependencies
    log_info "Installing dependencies..."
    yarn install

    # Compile TypeScript
    log_info "Compiling TypeScript..."
    yarn compile

    # Create build directory
    log_info "Creating build package..."
    rm -rf $BUILD_PATH
    mkdir -p $BUILD_PATH

    # Copy necessary files
    cp package.json $BUILD_PATH/
    cp yarn.lock $BUILD_PATH/
    cp ecosystem.config.js $BUILD_PATH/
    cp -r lib $BUILD_PATH/
    cp -r config $BUILD_PATH/
    cp -r public $BUILD_PATH/

    # Create environment-specific config if it doesn't exist
    if [[ ! -f "config/${ENVIRONMENT}.json" ]]; then
        log_warning "Environment config config/${ENVIRONMENT}.json not found, using production.json"
        cp config/production.json $BUILD_PATH/config/${ENVIRONMENT}.json
    fi

    log_success "Application built successfully"
}

# Function to deploy files to remote server
deploy_files() {
    log_info "Deploying files to remote server..."

    # Create remote directory if it doesn't exist
    $SSH_CMD "sudo mkdir -p $REMOTE_PATH && sudo chown $REMOTE_USER:$REMOTE_USER $REMOTE_PATH"

    # Stop the application before deployment
    log_info "Stopping application..."
    $SSH_CMD "cd $REMOTE_PATH && pm2 stop $APP_NAME || true"

    # Backup current deployment
    log_info "Creating backup of current deployment..."
    $SSH_CMD "cd $REMOTE_PATH && if [ -d lib ]; then sudo rm -rf backup && sudo mkdir -p backup && sudo cp -r lib config package.json ecosystem.config.js backup/ 2>/dev/null || true; fi"

    # Transfer files
    log_info "Transferring files..."
    rsync -avz --delete -e "ssh -i $SSH_KEY -p $SSH_PORT -o StrictHostKeyChecking=no" \
        $BUILD_PATH/ $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/

    log_success "Files deployed successfully"
}

# Function to install dependencies on remote server
install_dependencies() {
    if [[ "$SKIP_DEPS" == true ]]; then
        log_warning "Skipping dependency installation"
        return
    fi

    log_info "Installing dependencies on remote server..."
    $SSH_CMD "cd $REMOTE_PATH && yarn install --production"
    log_success "Dependencies installed successfully"
}

# Function to start the application
start_application() {
    log_info "Starting application with PM2..."

    # Set NODE_ENV
    $SSH_CMD "cd $REMOTE_PATH && export NODE_ENV=$ENVIRONMENT"

    # Start or restart the application
    $SSH_CMD "cd $REMOTE_PATH && pm2 start ecosystem.config.js --env $ENVIRONMENT || pm2 restart ecosystem.config.js --env $ENVIRONMENT"

    # Save PM2 configuration
    $SSH_CMD "pm2 save"

    log_success "Application started successfully"
}

# Function to check application health
check_health() {
    log_info "Checking application health..."

    # Wait a moment for the application to start
    sleep 5

    # Check PM2 status
    $SSH_CMD "pm2 status $APP_NAME"

    # Check if the application is responding
    APP_PORT=$($SSH_CMD "cd $REMOTE_PATH && node -e \"console.log(require('./config/${ENVIRONMENT}.json').port || 3031)\"")

    if $SSH_CMD "curl -f http://localhost:$APP_PORT/health > /dev/null 2>&1" || $SSH_CMD "curl -f http://localhost:$APP_PORT > /dev/null 2>&1"; then
        log_success "Application is healthy and responding"
    else
        log_warning "Application health check failed, but PM2 shows it's running"
        log_info "Check the application logs: pm2 logs $APP_NAME"
    fi
}

# Function to show deployment summary
show_summary() {
    log_info "Deployment Summary:"
    echo "  Host: $REMOTE_HOST"
    echo "  Environment: $ENVIRONMENT"
    echo "  Remote Path: $REMOTE_PATH"
    echo "  Application: $APP_NAME"
    echo ""
    log_info "Useful commands:"
    echo "  SSH to server: $SSH_CMD"
    echo "  Check PM2 status: $SSH_CMD 'pm2 status'"
    echo "  View logs: $SSH_CMD 'pm2 logs $APP_NAME'"
    echo "  Restart app: $SSH_CMD 'cd $REMOTE_PATH && pm2 restart $APP_NAME'"
}

# Cleanup function
cleanup() {
    if [[ -d "$BUILD_PATH" ]]; then
        log_info "Cleaning up build directory..."
        rm -rf $BUILD_PATH
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Main deployment process
main() {
    log_info "=== CTR Road Name Translation Deployment ==="

    test_ssh_connection
    build_application
    deploy_files
    install_dependencies
    start_application
    check_health
    show_summary

    log_success "Deployment completed successfully!"
}

# Run main function
main